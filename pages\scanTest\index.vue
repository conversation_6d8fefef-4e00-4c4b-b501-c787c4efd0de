<template>
	<view class="scan-test">
		<view class="header">
			<text class="title">扫码功能测试</text>
		</view>
		
		<view class="content">
			<view class="test-section">
				<text class="section-title">当前环境</text>
				<text class="environment">{{ currentEnv }}</text>
			</view>
			
			<view class="test-section">
				<text class="section-title">扫码结果</text>
				<view class="result-box">
					<text v-if="scanResult" class="result-text">{{ scanResult }}</text>
					<text v-else class="placeholder">暂无扫码结果</text>
				</view>
			</view>
			
			<view class="button-section">
				<button class="scan-btn" @click="startScan" :disabled="isScanning">
					{{ isScanning ? '扫码中...' : '开始扫码' }}
				</button>
				<button class="clear-btn" @click="clearResult">清除结果</button>
			</view>
		</view>
		
		<!-- H5扫码弹窗 -->
		<view v-if="showH5Scanner" class="h5-scanner-modal" @click="closeScanner">
			<view class="scanner-container" @click.stop>
				<view class="scanner-header">
					<text class="scanner-title">扫描二维码</text>
					<text class="close-btn" @click="closeScanner">×</text>
				</view>
				<view class="scanner-content">
					<view id="qr-reader" class="qr-reader"></view>
				</view>
				<view class="scanner-tip">
					请将二维码放入框内进行扫描
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import scanCodeUtil from '../../utils/scanCode.js'

// 响应式数据
const currentEnv = ref('')
const scanResult = ref('')
const isScanning = ref(false)
const showH5Scanner = ref(false)

// 生命周期
onMounted(() => {
	// 检测当前环境
	if (scanCodeUtil.isH5()) {
		currentEnv.value = 'H5环境'
	} else {
		currentEnv.value = '非H5环境（App/小程序）'
	}
})

// 开始扫码
const startScan = () => {
	if (isScanning.value) return
	
	isScanning.value = true
	scanResult.value = ''
	
	if (scanCodeUtil.isH5()) {
		// H5环境显示弹窗
		showH5Scanner.value = true
		// 等待DOM更新后启动扫码
		setTimeout(() => {
			performScan()
		}, 100)
	} else {
		// 其他环境直接扫码
		performScan()
	}
}

// 执行扫码
const performScan = () => {
	scanCodeUtil.startScan({
		success: (result) => {
			console.log('扫码成功:', result)
			scanResult.value = result
			closeScanner()
			uni.showToast({
				title: '扫码成功',
				icon: 'success'
			})
		},
		fail: (error) => {
			console.error('扫码失败:', error)
			closeScanner()
			uni.showToast({
				title: '扫码失败',
				icon: 'none'
			})
		},
		containerId: 'qr-reader'
	})
}

// 关闭扫码器
const closeScanner = () => {
	scanCodeUtil.stopScan()
	showH5Scanner.value = false
	isScanning.value = false
}

// 清除结果
const clearResult = () => {
	scanResult.value = ''
}

// 组件卸载时清理
onUnmounted(() => {
	closeScanner()
})
</script>

<style lang="scss" scoped>
.scan-test {
	padding: 20px;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	text-align: center;
	margin-bottom: 30px;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.content {
	background-color: #fff;
	border-radius: 12px;
	padding: 20px;
}

.test-section {
	margin-bottom: 30px;
}

.section-title {
	display: block;
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 10px;
}

.environment {
	font-size: 14px;
	color: #666;
	padding: 10px;
	background-color: #f8f9fa;
	border-radius: 6px;
}

.result-box {
	min-height: 80px;
	padding: 15px;
	border: 2px dashed #ddd;
	border-radius: 8px;
	background-color: #fafafa;
	display: flex;
	align-items: center;
	justify-content: center;
}

.result-text {
	font-size: 14px;
	color: #333;
	word-break: break-all;
	text-align: center;
}

.placeholder {
	font-size: 14px;
	color: #999;
}

.button-section {
	display: flex;
	gap: 15px;
}

.scan-btn, .clear-btn {
	flex: 1;
	height: 44px;
	border-radius: 8px;
	font-size: 16px;
	border: none;
}

.scan-btn {
	background-color: #007aff;
	color: white;
}

.scan-btn:disabled {
	background-color: #ccc;
}

.clear-btn {
	background-color: #f8f9fa;
	color: #666;
	border: 1px solid #ddd;
}

/* H5扫码弹窗样式 */
.h5-scanner-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.scanner-container {
	background-color: #fff;
	border-radius: 12px;
	width: 90%;
	max-width: 400px;
	max-height: 80%;
	overflow: hidden;
}

.scanner-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	border-bottom: 1px solid #eee;
	background-color: #f8f9fa;
}

.scanner-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.close-btn {
	font-size: 24px;
	color: #666;
	cursor: pointer;
	width: 30px;
	height: 30px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	transition: background-color 0.2s;
}

.close-btn:hover {
	background-color: #e9ecef;
}

.scanner-content {
	padding: 20px;
	display: flex;
	justify-content: center;
}

.qr-reader {
	width: 100%;
	max-width: 300px;
}

.scanner-tip {
	text-align: center;
	padding: 16px 20px;
	color: #666;
	font-size: 14px;
	background-color: #f8f9fa;
	border-top: 1px solid #eee;
}

/* 覆盖html5-qrcode默认样式 */
:deep(#qr-reader) {
	border: none !important;
}

:deep(#qr-reader__dashboard_section) {
	display: none !important;
}

:deep(#qr-reader__camera_selection) {
	margin-bottom: 10px !important;
}

:deep(#qr-reader__scan_region) {
	border-radius: 8px !important;
	overflow: hidden !important;
}

:deep(#qr-reader__scan_region video) {
	border-radius: 8px !important;
}
</style>
