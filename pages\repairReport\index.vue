<template>
	<view class="repair-report">
		<view class="tab-bar">
			<view class="tab-item" :class="{ active: activeTab === 'report' }" @click="switchTab('report')">
				故障报修
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'records' }" @click="switchTab('records')">
				报修记录
			</view>
		</view>

		<!-- 故障报修表单 -->
		<view v-if="activeTab === 'report'" class="form-content">
			<view class="device-scan">
				<view class="left">
					<view class="label">
						故障设备
					</view>
					<view class="tip">
						{{reportForm.deviceName || "当前未获取故障设备"}}
					</view>
				</view>
				<view class="right" @click="handleScan()">
					<view class="tip">
						扫码获取故障设备
					</view>
					<text class="scan-icon">📷</text>
				</view>
			</view>

			<view class="form-item">
				<text class="label">设备类型：</text>
				<picker @change="deviceTypeChange" range-key="label" :range="deviceTypeList">
					<view class="picker-input">{{getDeviceTypeLabel(reportForm.deviceType) || '请选择设备类型'}}</view>
				</picker>
			</view>

			<view class="form-item">
				<text class="label">设备机号：</text>
				<input class="input" v-model="reportForm.deviceNo" placeholder="请输入设备机号" />
			</view>
			<view class="form-item">
				<text class="label">设备码：</text>
				<input class="input" v-model="reportForm.deviceCode" placeholder="请输入设备码" />
			</view>

			<view class="form-item">
				<text class="label">联系电话：</text>
				<input class="input" type="number" v-model="reportForm.phone" placeholder="请输入联系电话" maxlength="11" />
			</view>

			<view class="form-item">
				<text class="label">设备位置：</text>
				<input class="input" v-model="reportForm.location" placeholder="请输入设备位置" />
			</view>

			<view class="form-item">
				<text class="label">问题描述：</text>
				<textarea class="textarea" v-model="reportForm.description" placeholder="请详细描述遇到的问题"
					maxlength="200"></textarea>
			</view>

			<view class="form-item">
				<text class="label">故障照片：</text>
				<view class="photo-upload">
					<view v-for="(photo, index) in reportForm.photos" :key="index" class="photo-item">
						<image :src="photo" class="photo-preview" @click="previewImage(photo)"></image>
						<text class="delete-photo" @click="deletePhoto(index)">×</text>
					</view>
					<view v-if="reportForm.photos.length < 3" class="add-photo" @click="chooseImage">
						<text class="iconfont icon-add"></text>
						<text class="add-text">添加照片</text>
					</view>
				</view>
			</view>

			<view class="button-group">
				<button class="cancel-btn" @click="goBack">取消</button>
				<button class="submit-btn" @click="submitReport">提交报修</button>
			</view>
		</view>

		<!-- 报修记录列表 -->
		<view v-if="activeTab === 'records'" class="records-content">
			<view v-if="repairRecords.length === 0" class="empty-state">
				<text class="empty-text">暂无报修记录</text>
			</view>
			<view v-else class="records-list">
				<view v-for="record in repairRecords" :key="record.id" class="record-item">
					<view class="record-header">
						<text class="record-id">报修单号：{{ record.id }}</text>
						<view class="status-badge" :class="record.status">
							{{ getStatusText(record.status) }}
						</view>
					</view>
					<view class="record-info">
						<text class="info-item">故障设备：{{ record.deviceName || '未填写' }}</text>
						<text class="info-item">设备类型：{{ record.deviceType || '未选择' }}</text>
						<text class="info-item">设备机号：{{ record.deviceNo || '未填写' }}</text>
						<text class="info-item">设备码：{{ record.deviceCode }}</text>
						<text class="info-item">设备位置：{{ record.location || '未填写' }}</text>
						<text class="info-item">报修时间：{{ record.createTime }}</text>
						<text class="info-item">问题描述：{{ record.description }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 响应式数据
const activeTab = ref('report')

// 报修表单数据
const reportForm = reactive({
	deviceName: '',
	deviceType: '',
	deviceNo: '',
	description: '',
	deviceCode: '',
	phone: '',
	location: '',
	photos: []
})

// 设备类型列表
const deviceTypeList = ref([
	{ label: '净水设备', value: '1' },
	{ label: '热水设备', value: '2' },
	{ label: '饮水机', value: '3' },
	{ label: '制冰机', value: '4' },
	{ label: '其他设备', value: '5' }
])

// 报修记录数据（模拟数据）
const repairRecords = ref([
	{
		id: 'R202312010001',
		deviceCode: 'DEV001',
		description: '设备无法正常出水',
		createTime: '2023-12-01 10:30',
		status: 'pending'
	},
	{
		id: 'R202311280002',
		deviceCode: 'DEV002',
		description: '设备显示屏异常',
		createTime: '2023-11-28 14:20',
		status: 'processing'
	},
	{
		id: 'R202311250003',
		deviceCode: 'DEV001',
		description: '设备按键失灵',
		createTime: '2023-11-25 09:15',
		status: 'completed'
	}
])

// 重置表单
const resetForm = () => {
	Object.assign(reportForm, {
		deviceName: '',
		deviceType: '',
		deviceNo: '',
		description: '',
		deviceCode: '',
		phone: '',
		location: '',
		photos: []
	})
}

// 设备类型选择
const deviceTypeChange = (e) => {
	const index = e.detail.value
	reportForm.deviceType = deviceTypeList.value[index].value
}

// 获取设备类型标签
const getDeviceTypeLabel = (value) => {
	const item = deviceTypeList.value.find(item => item.value === value)
	return item ? item.label : ''
}

// 扫码获取设备信息
const handleScan = () => {
	// 导入扫码工具
	import('../../utils/scanCode.js').then(module => {
		const scanCodeUtil = module.default

		scanCodeUtil.startScan({
			success: (result) => {
				console.log('扫码成功:', result)
				processDeviceInfo(result)
			},
			fail: (error) => {
				console.error('扫码失败:', error)
				uni.showToast({
					title: "识别二维码失败！",
					icon: "none"
				})
			}
		})
	}).catch(error => {
		console.error('加载扫码工具失败:', error)
		uni.showToast({
			title: "扫码功能加载失败",
			icon: "none"
		})
	})
}

// 处理设备信息
const processDeviceInfo = (scanResult) => {
	uni.showLoading({
		title: "加载中...",
		mask: true
	})

	try {
		// 这里可以调用API获取设备信息
		// 模拟API调用
		setTimeout(() => {
			// 模拟从扫码结果获取设备信息
			reportForm.deviceName = "智能净水设备-001"
			reportForm.deviceType = "1" // 净水设备
			reportForm.deviceNo = "SN" + scanResult.slice(-6)
			reportForm.deviceCode = scanResult
			reportForm.location = "办公楼1层大厅"

			uni.hideLoading()
			uni.showToast({
				title: "设备信息获取成功",
				icon: "success"
			})
		}, 1000)
	} catch (error) {
		uni.hideLoading()
		uni.showToast({
			title: "获取设备信息失败",
			icon: "none"
		})
	}
}

// 切换标签页
const switchTab = (tab) => {
	activeTab.value = tab
}

// 返回首页
const goBack = () => {
	uni.navigateBack({
		delta: 1
	})
}

// 选择图片
const chooseImage = () => {
	uni.chooseImage({
		count: 3 - reportForm.photos.length,
		sizeType: ['compressed'],
		sourceType: ['camera', 'album'],
		success: (res) => {
			reportForm.photos.push(...res.tempFilePaths)
		}
	})
}

// 删除照片
const deletePhoto = (index) => {
	reportForm.photos.splice(index, 1)
}

// 预览图片
const previewImage = (src) => {
	uni.previewImage({
		urls: [src],
		current: src
	})
}

// 提交报修
const submitReport = () => {
	// 验证表单
	if (!reportForm.deviceName.trim()) {
		uni.showToast({
			title: '请填写故障设备名称',
			icon: 'none'
		})
		return
	}

	if (!reportForm.deviceType) {
		uni.showToast({
			title: '请选择设备类型',
			icon: 'none'
		})
		return
	}

	if (!reportForm.deviceNo.trim()) {
		uni.showToast({
			title: '请填写设备机号',
			icon: 'none'
		})
		return
	}

	if (!reportForm.description.trim()) {
		uni.showToast({
			title: '请填写问题描述',
			icon: 'none'
		})
		return
	}

	if (!reportForm.deviceCode.trim()) {
		uni.showToast({
			title: '请填写设备码',
			icon: 'none'
		})
		return
	}

	if (!reportForm.phone.trim()) {
		uni.showToast({
			title: '请填写联系电话',
			icon: 'none'
		})
		return
	}

	if (!/^1[3-9]\d{9}$/.test(reportForm.phone)) {
		uni.showToast({
			title: '请填写正确的手机号码',
			icon: 'none'
		})
		return
	}

	if (!reportForm.location.trim()) {
		uni.showToast({
			title: '请填写设备位置',
			icon: 'none'
		})
		return
	}

	// 提交报修
	uni.showLoading({
		title: '提交中...'
	})
		console.log('提交的数据', reportForm);

	// 模拟API调用
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: '报修提交成功',
			icon: 'success'
		})

		// 添加到记录列表（模拟）
		const newRecord = {
			id: 'R' + Date.now(),
			deviceName: reportForm.deviceName,
			deviceType: getDeviceTypeLabel(reportForm.deviceType),
			deviceNo: reportForm.deviceNo,
			deviceCode: reportForm.deviceCode,
			description: reportForm.description,
			location: reportForm.location,
			phone: reportForm.phone,
			createTime: new Date().toLocaleString(),
			status: 'pending'
		}
		repairRecords.value.unshift(newRecord)

		resetForm()
		activeTab.value = 'records'
	}, 1500)
}

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		pending: '待处理',
		processing: '处理中',
		completed: '已完成'
	}
	return statusMap[status] || '未知状态'
}
</script>

<style lang="scss" scoped>
@import url('./index.min.css');
</style>
