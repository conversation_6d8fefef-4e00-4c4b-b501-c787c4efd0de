# H5端扫码功能解决方案

## 问题描述
uni.scanCode 在 H5 端不支持，需要使用第三方库来实现扫码功能。

## 解决方案
使用 `html5-qrcode` 库在 H5 端实现扫码功能，同时保持其他端使用 `uni.scanCode` 的兼容性。

## 实现内容

### 1. 通用扫码工具类
- 文件位置：`utils/scanCode.js`
- 功能：自动检测运行环境，H5端使用html5-qrcode，其他端使用uni.scanCode
- 特性：
  - 单例模式，避免重复初始化
  - 统一的API接口
  - 自动环境检测
  - 错误处理

### 2. 更新的页面
- `pages/scanWater/index.vue` - 扫码用水页面
- `pages/repairReport/index.vue` - 故障报修页面
- `pages/scanTest/index.vue` - 扫码测试页面（新增）

### 3. 使用方法

#### 基本用法
```javascript
import scanCodeUtil from '../../utils/scanCode.js'

// 开始扫码
scanCodeUtil.startScan({
  success: (result) => {
    console.log('扫码成功:', result)
    // 处理扫码结果
  },
  fail: (error) => {
    console.error('扫码失败:', error)
    // 处理扫码失败
  },
  containerId: 'qr-reader' // H5端扫码容器ID
})

// 停止扫码
scanCodeUtil.stopScan()

// 检查是否正在扫码
const isScanning = scanCodeUtil.getIsScanning()
```

#### H5端扫码弹窗实现
```vue
<template>
  <!-- H5扫码弹窗 -->
  <view v-if="showH5Scanner" class="h5-scanner-modal" @click="closeScanner">
    <view class="scanner-container" @click.stop>
      <view class="scanner-header">
        <text class="scanner-title">扫描二维码</text>
        <text class="close-btn" @click="closeScanner">×</text>
      </view>
      <view class="scanner-content">
        <view id="qr-reader" class="qr-reader"></view>
      </view>
      <view class="scanner-tip">
        请将二维码放入框内进行扫描
      </view>
    </view>
  </view>
</template>
```

### 4. 环境检测
工具类会自动检测当前运行环境：
- H5环境：使用 html5-qrcode 库
- 其他环境（App/小程序）：使用 uni.scanCode

### 5. 依赖库
项目已安装 `html5-qrcode@2.3.8`，无需额外安装。

### 6. 测试方法
1. 在首页点击"扫码测试"按钮
2. 进入测试页面查看当前环境
3. 点击"开始扫码"测试扫码功能
4. H5端会弹出扫码弹窗，其他端直接调用系统扫码

### 7. 注意事项
- H5端需要HTTPS环境才能访问摄像头
- 首次使用需要用户授权摄像头权限
- 扫码弹窗支持点击遮罩层关闭
- 组件卸载时会自动清理扫码器资源

### 8. 样式定制
扫码弹窗样式可以通过修改对应的CSS类进行定制：
- `.h5-scanner-modal` - 弹窗遮罩层
- `.scanner-container` - 扫码容器
- `.scanner-header` - 弹窗头部
- `.scanner-content` - 扫码区域
- `.qr-reader` - 扫码器容器

### 9. 错误处理
- 库加载失败：显示"扫码功能加载失败"提示
- 摄像头权限被拒绝：显示"扫码失败"提示
- 扫码超时：用户可手动关闭弹窗

## 兼容性
- ✅ H5端：使用html5-qrcode库
- ✅ App端：使用uni.scanCode
- ✅ 小程序端：使用uni.scanCode
- ✅ 支持二维码和条形码识别
