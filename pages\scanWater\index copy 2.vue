<template>
	<!-- 扫码用水 -->
	<view class="function-btn" @click="scanWater">
		<text class="iconfont">&#xe600;</text>
		<text class="btn-text">扫码用水</text>
	</view>

	<!-- H5扫码弹窗 -->
	<view v-if="showH5Scanner" class="h5-scanner-modal" @click="closeH5Scanner">
		<view class="scanner-container" @click.stop>
			<view class="scanner-header">
				<text class="scanner-title">扫描二维码</text>
				<text class="close-btn" @click="closeH5Scanner">×</text>
			</view>
			<view class="scanner-content">
				<view id="qr-reader" class="qr-reader"></view>
			</view>
			<view class="scanner-tip">
				请将二维码放入框内进行扫描
			</view>
		</view>
	</view>
</template>

<script setup>

import { ref, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import scanCodeUtil from '../../utils/scanCode.js'

// 响应式数据
const showH5Scanner = ref(false)

// 生命周期钩子
onLoad(() => {
	// 原来的 onLoad 逻辑可以放在这里
})

// 开始扫码
const scanWater = () => {
	console.log('开始扫码')

	if (scanCodeUtil.isH5()) {
		// H5环境显示弹窗
		showH5Scanner.value = true
		// 等待DOM更新后启动扫码
		setTimeout(() => {
			startScan()
		}, 100)
	} else {
		// 其他环境直接扫码
		startScan()
	}
}

// 启动扫码
const startScan = () => {
	scanCodeUtil.startScan({
		success: (result) => {
			console.log('扫码成功:', result)
			closeH5Scanner()
			handleScanResult(result)
		},
		fail: (error) => {
			console.error('扫码失败:', error)
			closeH5Scanner()
			uni.showToast({
				title: '扫码失败',
				icon: 'none'
			})
		},
		containerId: 'qr-reader'
	})
}

// 关闭H5扫码器
const closeH5Scanner = () => {
	scanCodeUtil.stopScan()
	showH5Scanner.value = false
}

// 处理扫码结果
const handleScanResult = (result) => {
	uni.showModal({
		title: '扫码成功',
		content: `扫码结果：${result}`,
		success: function (res) {
			if (res.confirm) {
				// 用户点击确定，可以进行下一步操作
				console.log('用户确认扫码结果')
				// 这里可以添加具体的用水逻辑
			}
		}
	})
}

// 组件卸载时清理
onUnmounted(() => {
	closeH5Scanner()
})

</script>
<style lang="scss" scoped>
.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}

/* H5扫码弹窗样式 */
.h5-scanner-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.scanner-container {
	background-color: #fff;
	border-radius: 12px;
	width: 90%;
	max-width: 400px;
	max-height: 80%;
	overflow: hidden;
}

.scanner-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	border-bottom: 1px solid #eee;
	background-color: #f8f9fa;
}

.scanner-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.close-btn {
	font-size: 24px;
	color: #666;
	cursor: pointer;
	width: 30px;
	height: 30px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	transition: background-color 0.2s;
}

.close-btn:hover {
	background-color: #e9ecef;
}

.scanner-content {
	padding: 20px;
	display: flex;
	justify-content: center;
}

.qr-reader {
	width: 100%;
	max-width: 300px;
}

.scanner-tip {
	text-align: center;
	padding: 16px 20px;
	color: #666;
	font-size: 14px;
	background-color: #f8f9fa;
	border-top: 1px solid #eee;
}

/* 覆盖html5-qrcode默认样式 */
:deep(#qr-reader) {
	border: none !important;
}

:deep(#qr-reader__dashboard_section) {
	display: none !important;
}

:deep(#qr-reader__camera_selection) {
	margin-bottom: 10px !important;
}

:deep(#qr-reader__scan_region) {
	border-radius: 8px !important;
	overflow: hidden !important;
}

:deep(#qr-reader__scan_region video) {
	border-radius: 8px !important;
}
</style>
