/**
 * 通用扫码工具类
 * 支持H5端使用html5-qrcode库，其他端使用uni.scanCode
 */

class ScanCodeUtil {
  constructor() {
    this.html5QrCode = null
    this.isScanning = false
  }

  /**
   * 判断是否为H5环境
   */
  isH5() {
    // #ifdef H5
    return true
    // #endif
    // #ifndef H5
    return false
    // #endif
  }

  /**
   * 开始扫码
   * @param {Object} options 扫码配置
   * @param {Function} options.success 成功回调
   * @param {Function} options.fail 失败回调
   * @param {String} options.containerId H5端扫码容器ID，默认为'qr-reader'
   */
  async startScan(options = {}) {
    const {
      success = () => {},
      fail = () => {},
      containerId = 'qr-reader'
    } = options

    if (this.isScanning) {
      console.warn('扫码正在进行中...')
      return
    }

    this.isScanning = true

    if (this.isH5()) {
      // H5环境使用html5-qrcode
      try {
        await this.startH5Scanner(containerId, success, fail)
      } catch (error) {
        console.error('H5扫码启动失败:', error)
        fail(error)
        this.isScanning = false
      }
    } else {
      // 其他环境使用uni.scanCode
      uni.scanCode({
        success: (res) => {
          console.log('扫码成功:', res.result)
          success(res.result, res)
          this.isScanning = false
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          fail(err)
          this.isScanning = false
        }
      })
    }
  }

  /**
   * 启动H5扫码器
   */
  async startH5Scanner(containerId, success, fail) {
    try {
      // 动态导入html5-qrcode
      const { Html5QrcodeScanner } = await import('html5-qrcode')
      
      // 等待DOM更新
      await new Promise(resolve => setTimeout(resolve, 100))
      
      this.html5QrCode = new Html5QrcodeScanner(
        containerId,
        { 
          fps: 10, 
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1.0,
          showTorchButtonIfSupported: true,
          showZoomSliderIfSupported: true
        },
        false
      )
      
      this.html5QrCode.render(
        (decodedText) => {
          console.log('H5扫码成功:', decodedText)
          this.stopScan()
          success(decodedText)
        },
        (error) => {
          // 这里不需要处理，html5-qrcode会持续尝试扫码
          // console.log('扫码中...', error)
        }
      )
    } catch (error) {
      console.error('H5扫码器初始化失败:', error)
      throw error
    }
  }

  /**
   * 停止扫码
   */
  stopScan() {
    if (this.html5QrCode) {
      this.html5QrCode.clear().catch(error => {
        console.error('清理H5扫码器失败:', error)
      })
      this.html5QrCode = null
    }
    this.isScanning = false
  }

  /**
   * 检查是否正在扫码
   */
  getIsScanning() {
    return this.isScanning
  }
}

// 创建单例实例
const scanCodeUtil = new ScanCodeUtil()

export default scanCodeUtil
